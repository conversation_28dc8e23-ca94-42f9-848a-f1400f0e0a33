import pandas as pd
import numpy as np
from collections import defaultdict
import re
import random

def read_student_data(file_path):
    """读取学生数据"""
    try:
        df = pd.read_excel(file_path)
        print("文件列名:", df.columns.tolist())
        print("数据形状:", df.shape)
        print("前5行数据:")
        print(df.head())
        return df
    except Exception as e:
        print(f"读取文件出错: {e}")
        return None

def get_pinyin_initial(name):
    """获取姓氏拼音首字母（简化版）"""
    # 常见姓氏拼音首字母映射
    surname_pinyin = {
        '李': 'L', '王': 'W', '张': 'Z', '刘': 'L', '陈': 'C', '杨': 'Y', '赵': 'Z', '黄': 'H',
        '周': 'Z', '吴': 'W', '徐': 'X', '孙': 'S', '胡': 'H', '朱': 'Z', '高': 'G', '林': 'L',
        '何': 'H', '郭': 'G', '马': 'M', '罗': 'L', '梁': 'L', '宋': 'S', '郑': 'Z', '谢': 'X',
        '韩': 'H', '唐': 'T', '冯': 'F', '于': 'Y', '董': 'D', '萧': 'X', '程': 'C', '曹': 'C',
        '袁': 'Y', '邓': 'D', '许': 'X', '傅': 'F', '沈': 'S', '曾': 'Z', '彭': 'P', '吕': 'L',
        '苏': 'S', '卢': 'L', '蒋': 'J', '蔡': 'C', '贾': 'J', '丁': 'D', '魏': 'W', '薛': 'X',
        '叶': 'Y', '阎': 'Y', '余': 'Y', '潘': 'P', '杜': 'D', '戴': 'D', '夏': 'X', '钟': 'Z',
        '汪': 'W', '田': 'T', '任': 'R', '姜': 'J', '范': 'F', '方': 'F', '石': 'S', '姚': 'Y',
        '谭': 'T', '廖': 'L', '邹': 'Z', '熊': 'X', '金': 'J', '陆': 'L', '郝': 'H', '孔': 'K',
        '白': 'B', '崔': 'C', '康': 'K', '毛': 'M', '邱': 'Q', '秦': 'Q', '江': 'J', '史': 'S',
        '顾': 'G', '侯': 'H', '邵': 'S', '孟': 'M', '龙': 'L', '万': 'W', '段': 'D', '漕': 'C',
        '钱': 'Q', '汤': 'T', '尹': 'Y', '黎': 'L', '易': 'Y', '常': 'C', '武': 'W', '乔': 'Q',
        '贺': 'H', '赖': 'L', '龚': 'G', '文': 'W'
    }
    
    if name and len(name) > 0:
        surname = name[0]
        return surname_pinyin.get(surname, surname)
    return 'A'

def analyze_students(df):
    """分析学生数据"""
    print("\n=== 学生数据分析 ===")
    
    # 检查必要的列
    required_columns = []
    for col in df.columns:
        print(f"列名: {col}")
        if '性别' in col or 'gender' in col.lower():
            required_columns.append(('gender', col))
        elif '姓名' in col or 'name' in col.lower():
            required_columns.append(('name', col))
        elif '等第' in col or 'grade' in col.lower() or '成绩' in col:
            required_columns.append(('grade', col))
        elif '备注' in col or 'note' in col.lower() or '说明' in col:
            required_columns.append(('note', col))
    
    print("识别的列:", required_columns)
    return required_columns

if __name__ == "__main__":
    print("开始运行分班程序...")

    # 读取学生数据
    file_path = "一年级学生名单（除特殊学生）.xlsx"
    print(f"正在读取文件: {file_path}")

    df = read_student_data(file_path)

    if df is not None:
        columns = analyze_students(df)
    else:
        print("无法读取学生数据文件")
