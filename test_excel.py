import pandas as pd

# 创建测试数据
data = {'姓名': ['张三', '李四'], '性别': ['男', '女'], '等第': ['A', 'B']}
df = pd.DataFrame(data)

print("测试数据:")
print(df)

# 尝试保存Excel文件
try:
    with pd.ExcelWriter('测试文件.xlsx', engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='测试', index=False)
    print("xlsxwriter引擎成功")
except Exception as e:
    print(f"xlsxwriter引擎失败: {e}")
    try:
        with pd.ExcelWriter('测试文件.xlsx', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='测试', index=False)
        print("openpyxl引擎成功")
    except Exception as e2:
        print(f"openpyxl引擎也失败: {e2}")
