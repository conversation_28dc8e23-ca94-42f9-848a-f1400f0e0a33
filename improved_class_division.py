#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from collections import defaultdict
import random
import re

def read_student_data(file_path):
    """读取学生数据"""
    print(f"正在读取文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print("文件读取成功!")
        print("文件列名:", df.columns.tolist())
        print("数据形状:", df.shape)
        print("前5行数据:")
        print(df.head())
        return df
    except Exception as e:
        print(f"读取文件出错: {e}")
        return None

def get_pinyin_initial(name):
    """获取姓氏拼音首字母（简化版）"""
    # 常见姓氏拼音首字母映射
    surname_pinyin = {
        '李': 'L', '王': 'W', '张': 'Z', '刘': 'L', '陈': 'C', '杨': 'Y', '赵': 'Z', '黄': 'H',
        '周': 'Z', '吴': 'W', '徐': 'X', '孙': 'S', '胡': 'H', '朱': 'Z', '高': 'G', '林': 'L',
        '何': 'H', '郭': 'G', '马': 'M', '罗': 'L', '梁': 'L', '宋': 'S', '郑': 'Z', '谢': 'X',
        '韩': 'H', '唐': 'T', '冯': 'F', '于': 'Y', '董': 'D', '萧': 'X', '程': 'C', '曹': 'C',
        '袁': 'Y', '邓': 'D', '许': 'X', '傅': 'F', '沈': 'S', '曾': 'Z', '彭': 'P', '吕': 'L',
        '苏': 'S', '卢': 'L', '蒋': 'J', '蔡': 'C', '贾': 'J', '丁': 'D', '魏': 'W', '薛': 'X',
        '叶': 'Y', '阎': 'Y', '余': 'Y', '潘': 'P', '杜': 'D', '戴': 'D', '夏': 'X', '钟': 'Z',
        '汪': 'W', '田': 'T', '任': 'R', '姜': 'J', '范': 'F', '方': 'F', '石': 'S', '姚': 'Y',
        '谭': 'T', '廖': 'L', '邹': 'Z', '熊': 'X', '金': 'J', '陆': 'L', '郝': 'H', '孔': 'K',
        '白': 'B', '崔': 'C', '康': 'K', '毛': 'M', '邱': 'Q', '秦': 'Q', '江': 'J', '史': 'S',
        '顾': 'G', '侯': 'H', '邵': 'S', '孟': 'M', '龙': 'L', '万': 'W', '段': 'D', '漕': 'C',
        '钱': 'Q', '汤': 'T', '尹': 'Y', '黎': 'L', '易': 'Y', '常': 'C', '武': 'W', '乔': 'Q',
        '贺': 'H', '赖': 'L', '龚': 'G', '文': 'W'
    }
    
    if name and len(name) > 0:
        surname = name[0]
        return surname_pinyin.get(surname, surname)
    return 'A'

def parse_special_requirements(df, column_mapping):
    """解析备注中的特殊要求"""
    note_col = column_mapping.get('note')
    name_col = column_mapping['name']
    
    same_class_pairs = []  # 需要同班的学生对
    twins = []  # 双胞胎
    special_notes = {}  # 其他特殊备注
    
    if note_col:
        for idx, row in df.iterrows():
            note = row[note_col]
            name = row[name_col]
            
            if pd.notna(note):
                note = str(note).strip()
                
                # 检查是否是双胞胎
                if '双胞胎' in note:
                    twins.append(idx)
                    print(f"发现双胞胎: {name}")
                
                # 检查是否要求与某人同班
                match = re.search(r'与(.+?)一个班', note)
                if match:
                    target_name = match.group(1)
                    # 查找目标学生
                    target_idx = df[df[name_col] == target_name].index
                    if len(target_idx) > 0:
                        same_class_pairs.append((idx, target_idx[0]))
                        print(f"同班要求: {name} 与 {target_name}")
                    else:
                        print(f"警告: 找不到学生 {target_name}，无法满足 {name} 的同班要求")
                
                # 记录其他特殊备注
                if note not in ['双胞胎，放一个班'] and '与' not in note:
                    special_notes[idx] = note
    
    return same_class_pairs, twins, special_notes

def divide_classes_improved(df, column_mapping, num_classes=8):
    """改进的分班算法，考虑特殊要求"""
    print(f"\n=== 开始分班，目标班级数: {num_classes} ===")
    
    name_col = column_mapping['name']
    gender_col = column_mapping['gender']
    grade_col = column_mapping['grade']
    
    # 添加拼音首字母列
    df['拼音首字母'] = df[name_col].apply(get_pinyin_initial)
    
    # 解析特殊要求
    same_class_pairs, twins, special_notes = parse_special_requirements(df, column_mapping)
    
    # 初始化班级
    classes = [[] for _ in range(num_classes)]
    class_stats = [{'male': 0, 'female': 0, 'total': 0} for _ in range(num_classes)]
    assigned = set()  # 已分配的学生索引
    
    # 首先处理特殊要求的学生
    print("\n处理特殊要求...")
    
    # 处理同班要求
    for student1_idx, student2_idx in same_class_pairs:
        if student1_idx not in assigned and student2_idx not in assigned:
            # 选择最合适的班级
            best_class = min(range(num_classes), key=lambda x: class_stats[x]['total'])
            
            # 分配两个学生到同一班级
            for idx in [student1_idx, student2_idx]:
                student = df.loc[idx]
                classes[best_class].append(student)
                class_stats[best_class]['total'] += 1
                if student[gender_col] == '男':
                    class_stats[best_class]['male'] += 1
                else:
                    class_stats[best_class]['female'] += 1
                assigned.add(idx)
            
            print(f"同班分配: {df.loc[student1_idx, name_col]} 和 {df.loc[student2_idx, name_col]} -> 班级{best_class+1}")
    
    # 处理双胞胎（如果还没被分配）
    unassigned_twins = [idx for idx in twins if idx not in assigned]
    if len(unassigned_twins) >= 2:
        # 每两个双胞胎分到同一班
        for i in range(0, len(unassigned_twins), 2):
            if i + 1 < len(unassigned_twins):
                twin1_idx = unassigned_twins[i]
                twin2_idx = unassigned_twins[i + 1]
                
                best_class = min(range(num_classes), key=lambda x: class_stats[x]['total'])
                
                for idx in [twin1_idx, twin2_idx]:
                    student = df.loc[idx]
                    classes[best_class].append(student)
                    class_stats[best_class]['total'] += 1
                    if student[gender_col] == '男':
                        class_stats[best_class]['male'] += 1
                    else:
                        class_stats[best_class]['female'] += 1
                    assigned.add(idx)
                
                print(f"双胞胎分配: {df.loc[twin1_idx, name_col]} 和 {df.loc[twin2_idx, name_col]} -> 班级{best_class+1}")
    
    # 处理剩余学生
    remaining_df = df[~df.index.isin(assigned)]
    print(f"\n处理剩余 {len(remaining_df)} 名学生...")
    
    # 按等第分组处理剩余学生
    grade_groups = remaining_df.groupby(grade_col)
    
    for grade, group in grade_groups:
        print(f"处理等第 {grade}，共 {len(group)} 人")
        
        # 按性别和拼音首字母排序
        group_sorted = group.sort_values([gender_col, '拼音首字母', name_col])
        
        # 轮流分配到各班
        for i, (idx, student) in enumerate(group_sorted.iterrows()):
            # 选择当前人数最少的班级
            min_class = min(range(num_classes), key=lambda x: class_stats[x]['total'])
            
            # 如果有多个班级人数相同，优先选择性别更平衡的班级
            min_total = class_stats[min_class]['total']
            candidates = [i for i in range(num_classes) if class_stats[i]['total'] == min_total]
            
            if len(candidates) > 1:
                gender = student[gender_col]
                if gender == '男':
                    min_class = min(candidates, key=lambda x: class_stats[x]['male'])
                else:
                    min_class = min(candidates, key=lambda x: class_stats[x]['female'])
            
            # 分配学生到班级
            classes[min_class].append(student)
            class_stats[min_class]['total'] += 1
            if student[gender_col] == '男':
                class_stats[min_class]['male'] += 1
            else:
                class_stats[min_class]['female'] += 1
    
    return classes, class_stats, special_notes

def save_class_sheets(classes, class_stats, column_mapping, special_notes, output_file="分班结果.xlsx"):
    """保存分班结果到Excel文件，每个班一个sheet"""
    print(f"\n=== 保存分班结果到 {output_file} ===")
    
    try:
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            for i, class_students in enumerate(classes):
                class_name = f"班级{i+1}"
                
                if class_students:
                    # 创建班级DataFrame
                    class_df = pd.DataFrame(class_students)
                    
                    # 重新排序列
                    columns_order = ['序号', column_mapping['name'], column_mapping['gender'], 
                                   column_mapping['grade'], '拼音首字母']
                    if column_mapping.get('note'):
                        columns_order.append(column_mapping['note'])
                    
                    class_df = class_df[columns_order]
                    
                    # 按姓氏拼音首字母和姓名排序
                    class_df = class_df.sort_values(['拼音首字母', column_mapping['name']])
                    
                    # 重新编号
                    class_df['班内序号'] = range(1, len(class_df) + 1)
                    class_df = class_df[['班内序号'] + [col for col in class_df.columns if col != '班内序号']]
                    
                    # 保存到sheet
                    class_df.to_excel(writer, sheet_name=class_name, index=False)
                    
                    print(f"{class_name}: {class_stats[i]['total']}人 (男{class_stats[i]['male']}人, 女{class_stats[i]['female']}人)")
                else:
                    # 创建空的DataFrame
                    empty_df = pd.DataFrame(columns=['班内序号', column_mapping['name'], 
                                                   column_mapping['gender'], column_mapping['grade']])
                    empty_df.to_excel(writer, sheet_name=class_name, index=False)
                    print(f"{class_name}: 0人")
        
        print(f"\n分班结果已保存到 {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    print("开始运行改进的分班程序...")
    
    # 读取学生数据
    file_path = "一年级学生名单（除特殊学生）.xlsx"
    
    df = read_student_data(file_path)
    
    if df is not None:
        # 分析数据
        column_mapping = {'name': '学生姓名', 'gender': '性别', 'grade': '等第', 'note': '备注'}
        
        print(f"\n总学生数: {len(df)}")
        print("性别分布:")
        print(df[column_mapping['gender']].value_counts())
        print("\n等第分布:")
        print(df[column_mapping['grade']].value_counts())
        
        # 执行分班
        classes, class_stats, special_notes = divide_classes_improved(df, column_mapping, num_classes=8)
        
        # 保存结果
        save_class_sheets(classes, class_stats, column_mapping, special_notes, "一年级分班结果.xlsx")
        
        print("\n=== 分班统计 ===")
        total_male = sum(stats['male'] for stats in class_stats)
        total_female = sum(stats['female'] for stats in class_stats)
        print(f"总计: {total_male + total_female}人 (男{total_male}人, 女{total_female}人)")
        
        # 显示班级平衡情况
        print("\n各班级详细统计:")
        for i, stats in enumerate(class_stats):
            ratio = stats['male'] / stats['total'] if stats['total'] > 0 else 0
            print(f"班级{i+1}: {stats['total']}人 (男{stats['male']}人 {ratio:.1%}, 女{stats['female']}人 {1-ratio:.1%})")
        
        print("\n程序运行完成!")
    else:
        print("无法读取学生数据文件")
